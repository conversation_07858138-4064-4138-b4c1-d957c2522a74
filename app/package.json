{"name": "molecule-preview", "private": true, "version": "0.0.0", "main": "index.js", "scripts": {"dev": "vite"}, "license": "ISC", "dependencies": {"@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "vite": "^4.4.9", "vite-plugin-mock-dev-server": "^1.4.0", "vite-plugin-monaco-editor": "^1.1.0", "vscode-oniguruma": "^2.0.1", "vscode-textmate": "6.x", "@dtinsight/molecule": "workspace:*"}}